"""
群发获客功能测试脚本
测试电话号码管理、数据库操作等核心功能
"""
import sys
import os

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from database.telenumber_operations import TelenumberOperations
from database.ensure_tables import ensure_telenumber_table_exists

def test_database_operations():
    """测试数据库操作"""
    print("=== 测试数据库操作 ===")
    
    # 确保表存在
    print("1. 确保telenumber表存在...")
    if ensure_telenumber_table_exists():
        print("✓ telenumber表已存在或创建成功")
    else:
        print("✗ telenumber表创建失败")
        return False
    
    # 创建操作实例
    phone_ops = TelenumberOperations()
    
    # 测试添加电话号码
    print("\n2. 测试添加电话号码...")
    test_numbers = [
        ("86", "13812345678", "测试号码1"),
        ("1", "2025551234", "美国测试号码"),
        ("44", "7700900123", "英国测试号码"),
    ]
    
    added_ids = []
    for country_code, local_number, notes in test_numbers:
        phone_id = phone_ops.add_phone_number(country_code, local_number, notes)
        if phone_id > 0:
            print(f"✓ 成功添加号码: +{country_code} {local_number} (ID: {phone_id})")
            added_ids.append(phone_id)
        else:
            print(f"✗ 添加号码失败: +{country_code} {local_number}")
    
    # 测试获取所有号码
    print("\n3. 测试获取所有电话号码...")
    all_numbers = phone_ops.get_all_active_phone_numbers()
    print(f"✓ 获取到 {len(all_numbers)} 个活跃号码")
    
    for number in all_numbers:
        print(f"  - {number['display_number']} ({number['whatsapp_id']})")
    
    # 测试获取WhatsApp ID列表
    print("\n4. 测试获取WhatsApp ID列表...")
    whatsapp_ids = phone_ops.get_whatsapp_ids_for_bulk_messaging()
    print(f"✓ 获取到 {len(whatsapp_ids)} 个WhatsApp ID")
    
    for wid in whatsapp_ids:
        print(f"  - {wid}")
    
    # 测试统计信息
    print("\n5. 测试统计信息...")
    stats = phone_ops.get_statistics()
    print(f"✓ 统计信息:")
    print(f"  - 总数: {stats['total_count']}")
    print(f"  - 活跃数: {stats['active_count']}")
    print(f"  - 已验证数: {stats['verified_count']}")
    print(f"  - 按国家分布: {stats['countries']}")
    
    # 测试搜索功能
    print("\n6. 测试搜索功能...")
    search_results = phone_ops.search_phone_numbers("138")
    print(f"✓ 搜索'138'找到 {len(search_results)} 个结果")
    
    # 测试按国家获取
    print("\n7. 测试按国家获取...")
    china_numbers = phone_ops.get_phone_numbers_by_country("86")
    print(f"✓ 中国号码数量: {len(china_numbers)}")
    
    # 测试更新号码
    print("\n8. 测试更新号码...")
    if added_ids:
        first_id = added_ids[0]
        if phone_ops.update_phone_number(first_id, notes="更新后的备注", is_verified=1):
            print(f"✓ 成功更新号码 ID: {first_id}")
        else:
            print(f"✗ 更新号码失败 ID: {first_id}")
    
    print("\n=== 数据库操作测试完成 ===")
    return True

def test_phone_number_validation():
    """测试电话号码验证"""
    print("\n=== 测试电话号码验证 ===")
    
    phone_ops = TelenumberOperations()
    
    # 测试有效号码
    valid_numbers = [
        ("86", "13812345678"),
        ("1", "2025551234"),
        ("44", "7700900123"),
        ("33", "123456789"),
    ]
    
    print("1. 测试有效号码...")
    for country_code, local_number in valid_numbers:
        if phone_ops._validate_phone_number(country_code, local_number):
            print(f"✓ 有效: +{country_code} {local_number}")
        else:
            print(f"✗ 无效: +{country_code} {local_number}")
    
    # 测试无效号码
    invalid_numbers = [
        ("", "13812345678"),  # 空国家代码
        ("86", ""),  # 空本地号码
        ("12345", "13812345678"),  # 国家代码太长
        ("86", "123"),  # 本地号码太短
        ("86", "123456789012345678"),  # 本地号码太长
        ("abc", "13812345678"),  # 非数字国家代码
        ("86", "abc12345678"),  # 非数字本地号码
    ]
    
    print("\n2. 测试无效号码...")
    for country_code, local_number in invalid_numbers:
        if not phone_ops._validate_phone_number(country_code, local_number):
            print(f"✓ 正确识别为无效: +{country_code} {local_number}")
        else:
            print(f"✗ 错误识别为有效: +{country_code} {local_number}")
    
    print("\n=== 电话号码验证测试完成 ===")

def test_whatsapp_format():
    """测试WhatsApp格式转换"""
    print("\n=== 测试WhatsApp格式转换 ===")
    
    phone_ops = TelenumberOperations()
    
    test_cases = [
        ("86", "13812345678", "<EMAIL>"),
        ("1", "2025551234", "<EMAIL>"),
        ("44", "7700900123", "<EMAIL>"),
    ]
    
    for country_code, local_number, expected_whatsapp_id in test_cases:
        # 清理输入
        clean_country = phone_ops._clean_country_code(country_code)
        clean_local = phone_ops._clean_local_number(local_number)
        
        # 生成WhatsApp ID
        whatsapp_id = f"{clean_country}{clean_local}@c.us"
        
        if whatsapp_id == expected_whatsapp_id:
            print(f"✓ 格式转换正确: +{country_code} {local_number} -> {whatsapp_id}")
        else:
            print(f"✗ 格式转换错误: +{country_code} {local_number} -> {whatsapp_id} (期望: {expected_whatsapp_id})")
    
    print("\n=== WhatsApp格式转换测试完成 ===")

def test_display_format():
    """测试显示格式"""
    print("\n=== 测试显示格式 ===")
    
    phone_ops = TelenumberOperations()
    
    test_cases = [
        ("86", "13812345678", "+86 13812345678"),
        ("1", "2025551234", "*************"),
        ("44", "7700900123", "+44 7700900123"),
    ]
    
    for country_code, local_number, expected_display in test_cases:
        display_number = phone_ops._format_display_number(country_code, local_number)
        
        if display_number == expected_display:
            print(f"✓ 显示格式正确: {display_number}")
        else:
            print(f"✗ 显示格式错误: {display_number} (期望: {expected_display})")
    
    print("\n=== 显示格式测试完成 ===")

def main():
    """主测试函数"""
    print("开始群发获客功能测试...")
    print("=" * 50)
    
    try:
        # 测试数据库操作
        if not test_database_operations():
            print("数据库操作测试失败，停止测试")
            return
        
        # 测试电话号码验证
        test_phone_number_validation()
        
        # 测试WhatsApp格式转换
        test_whatsapp_format()
        
        # 测试显示格式
        test_display_format()
        
        print("\n" + "=" * 50)
        print("✓ 所有测试完成！群发获客功能基础组件工作正常。")
        print("\n注意事项：")
        print("1. WhatsApp Web.js服务需要单独测试（需要真实的WhatsApp账号）")
        print("2. 群发功能需要在有网络连接的环境中测试")
        print("3. 建议先用少量测试号码验证群发功能")
        print("4. 确保遵守WhatsApp的使用条款和反垃圾邮件政策")
        
    except Exception as e:
        print(f"\n✗ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
