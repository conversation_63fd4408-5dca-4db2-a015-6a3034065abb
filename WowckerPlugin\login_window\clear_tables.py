#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
清空用户表、店铺表，以及清理超过48小时未更新的上下文记录
"""

import os
import sys
import sqlite3
import logging
import time
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

# 确保能导入主程序
MAIN_DIR = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(MAIN_DIR)

# 导入数据库模块
from database.app_db_manager import AppDBManager

def clear_database_tables():
    """
    清空所有数据库表内容
    
    Args:
        无
        
    Returns:
        无，但会在控制台输出操作结果日志
    """
    conn = None
    try:
        # 初始化数据库管理器
        db_manager = AppDBManager()
        db_manager.initialize()
        
        # 直接使用底层的DatabaseManager连接数据库
        db_manager.db_manager.connect()
        conn = db_manager.db_manager.conn
        cursor = conn.cursor()
        
        # 暂时禁用外键约束，以避免删除时的外键错误
        cursor.execute("PRAGMA foreign_keys = OFF")
        
        # 获取所有表名
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
        tables = [table[0] for table in cursor.fetchall()]
        logging.info(f"发现以下表: {', '.join(tables)}")
        
        # 要跳过的表名列表
        skip_tables = ['intent_analysis', 'contexts', 'telenumber']
        
        # 清空需要清空的表
        for table in tables:
            # 如果表在跳过列表中，则跳过
            if table in skip_tables:
                logging.info(f"跳过清空表: {table}")
                continue
                
            try:
                cursor.execute(f"DELETE FROM {table}")
                logging.info(f"已清空表: {table}")
            except sqlite3.Error as e:
                logging.warning(f"清空表 {table} 时出现错误: {str(e)}")
        
        # 重新启用外键约束
        cursor.execute("PRAGMA foreign_keys = ON")
        
        # 提交事务
        conn.commit()
        logging.info("所有数据库表清理操作成功完成")
        
    except sqlite3.Error as e:
        logging.error(f"清理数据库表时发生错误: {e}")
        # 如果发生错误，回滚事务
        if conn:
            conn.rollback()
    finally:
        # 恢复外键约束（以防异常导致提前退出）
        if conn:
            try:
                cursor = conn.cursor()
                cursor.execute("PRAGMA foreign_keys = ON")
                conn.commit()
            except:
                pass
                
        # 使用DatabaseManager的close方法关闭连接
        if db_manager and hasattr(db_manager, 'db_manager'):
            db_manager.db_manager.close()

if __name__ == "__main__":
    clear_database_tables()
