"""
测试修复后的群发获客功能
"""
import sys
import os
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from database.telenumber_operations import TelenumberOperations
from chat.whatsapp.bulk_messaging_service import BulkMessagingService

def test_telenumber_persistence():
    """测试telenumber表数据持久性"""
    print("=== 测试telenumber表数据持久性 ===")
    
    phone_ops = TelenumberOperations()
    
    # 添加测试数据
    print("1. 添加测试电话号码...")
    test_numbers = [
        ("86", "13800138001", "测试号码1"),
        ("1", "2025551001", "美国测试号码"),
        ("44", "7700900001", "英国测试号码"),
    ]
    
    added_ids = []
    for country_code, local_number, notes in test_numbers:
        phone_id = phone_ops.add_phone_number(country_code, local_number, notes)
        if phone_id > 0:
            print(f"✓ 成功添加: +{country_code} {local_number} (ID: {phone_id})")
            added_ids.append(phone_id)
        else:
            print(f"- 号码可能已存在: +{country_code} {local_number}")
    
    # 检查数据
    print("\n2. 检查当前数据...")
    all_numbers = phone_ops.get_all_active_phone_numbers()
    print(f"✓ 当前活跃号码数量: {len(all_numbers)}")
    
    for number in all_numbers:
        print(f"  - {number['display_number']} ({number['whatsapp_id']})")
    
    # 模拟清空表操作（但telenumber应该被跳过）
    print("\n3. 测试清空表操作...")
    try:
        from login_window.clear_tables import clear_database_tables
        print("执行清空表操作...")
        clear_database_tables()
        
        # 再次检查数据
        print("\n4. 清空表后检查数据...")
        all_numbers_after = phone_ops.get_all_active_phone_numbers()
        print(f"✓ 清空表后活跃号码数量: {len(all_numbers_after)}")
        
        if len(all_numbers_after) == len(all_numbers):
            print("✓ telenumber表数据已保留，修复成功！")
        else:
            print("✗ telenumber表数据被清空，需要检查修复")
            
    except Exception as e:
        print(f"✗ 清空表操作失败: {str(e)}")
    
    return len(all_numbers)

def test_whatsapp_status_detection():
    """测试WhatsApp状态检测修复"""
    print("\n=== 测试WhatsApp状态检测修复 ===")
    
    service = BulkMessagingService()
    
    try:
        print("1. 启动WhatsApp服务...")
        if service.start():
            print("✓ 服务启动成功")
            
            # 等待状态稳定
            print("2. 监控状态变化...")
            for i in range(10):
                time.sleep(1)
                detailed_status = service.get_detailed_status()
                node_status = detailed_status.get('node_status', 'unknown')
                is_running = detailed_status.get('is_running', False)
                
                print(f"  [{i+1}] Node状态: {node_status}, 运行: {is_running}")
                
                if node_status in ['qr_received', 'ready', 'auth_failure']:
                    print(f"✓ 达到稳定状态: {node_status}")
                    break
            
            # 测试群发消息状态检查
            print("\n3. 测试群发消息状态检查...")
            final_status = service.get_detailed_status()
            node_status = final_status.get('node_status', 'unknown')
            
            if node_status == 'ready':
                print("✓ 状态为ready，可以测试群发")
                # 这里不实际发送，只测试状态检查
                print("  (跳过实际群发测试，避免发送消息)")
            elif node_status == 'qr_received':
                print("✓ 状态为qr_received，需要扫码登录")
                print("  如果扫码登录后状态应该变为ready")
            else:
                print(f"- 当前状态: {node_status}")
            
            # 测试强制关闭
            print("\n4. 测试强制关闭...")
            if service.force_close():
                print("✓ 服务强制关闭成功")
            else:
                print("✗ 服务强制关闭失败")
                
        else:
            print("✗ 服务启动失败")
            
    except Exception as e:
        print(f"✗ WhatsApp状态检测测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            service.force_close()
        except:
            pass

def test_bulk_send_status_check():
    """测试群发消息的状态检查逻辑"""
    print("\n=== 测试群发消息状态检查逻辑 ===")
    
    service = BulkMessagingService()
    
    try:
        # 测试未启动状态
        print("1. 测试未启动状态的群发检查...")
        result = service.send_bulk_messages("测试消息")
        if not result:
            print("✓ 未启动状态正确拒绝群发")
        else:
            print("✗ 未启动状态错误允许群发")
        
        # 启动服务
        print("\n2. 启动服务并测试状态检查...")
        if service.start():
            time.sleep(3)  # 等待初始化
            
            detailed_status = service.get_detailed_status()
            node_status = detailed_status.get('node_status', 'unknown')
            
            print(f"当前Node状态: {node_status}")
            
            # 测试非ready状态的群发检查
            if node_status != 'ready':
                result = service.send_bulk_messages("测试消息")
                if not result:
                    print(f"✓ {node_status}状态正确拒绝群发")
                else:
                    print(f"✗ {node_status}状态错误允许群发")
            else:
                print("✓ 状态为ready，群发功能应该可用")
                print("  (跳过实际群发测试)")
        
        # 关闭服务
        service.force_close()
        
    except Exception as e:
        print(f"✗ 群发状态检查测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

def main():
    """主测试函数"""
    print("开始测试修复后的群发获客功能...")
    print("=" * 60)
    
    # 测试1: telenumber表数据持久性
    phone_count = test_telenumber_persistence()
    
    # 测试2: WhatsApp状态检测
    test_whatsapp_status_detection()
    
    # 测试3: 群发消息状态检查
    test_bulk_send_status_check()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n修复总结:")
    print("1. ✅ telenumber表已添加到跳过清空列表")
    print("2. ✅ WhatsApp状态检测已改进，使用详细状态判断")
    print("3. ✅ 群发消息现在检查node_status是否为'ready'")
    print("4. ✅ 添加了重置账号和关闭服务按钮")
    
    if phone_count > 0:
        print(f"\n当前数据库中有 {phone_count} 个电话号码，登录时不会被清空。")
    
    print("\n使用建议:")
    print("- 扫码登录成功后，状态应显示'已登录'")
    print("- 只有在状态为'已登录'时才能进行群发")
    print("- 使用'重置账号'可以清除登录信息重新登录")
    print("- 使用'关闭服务'可以完全停止WhatsApp服务")

if __name__ == "__main__":
    main()
