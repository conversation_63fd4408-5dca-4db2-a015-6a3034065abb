# 群发获客功能使用说明

## 功能概述

群发获客功能是一个基于WhatsApp Web.js的批量消息发送系统，支持电话号码管理、WhatsApp登录、消息编辑和批量发送等完整功能。

## 主要特性

### 1. 电话号码管理
- **国际号码支持**：支持全球各国电话号码格式
- **智能格式转换**：自动转换为WhatsApp Web.js要求的格式
- **用户友好显示**：以 `+国家代码 本地号码` 格式显示
- **批量操作**：支持批量添加、删除和管理电话号码

### 2. WhatsApp集成
- **QR码登录**：支持扫描QR码登录WhatsApp Web
- **会话持久化**：登录状态可以跨会话保持
- **多账号支持**：支持不同的WhatsApp账号登录
- **状态监控**：实时显示连接和登录状态

### 3. 群发消息
- **批量发送**：一键向所有号码发送消息
- **发送间隔**：自动控制发送间隔，避免被限制
- **进度跟踪**：实时显示发送进度和状态
- **错误处理**：记录发送失败的号码和原因

## 技术架构

### 数据库设计
```sql
CREATE TABLE telenumber (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    country_code TEXT NOT NULL,           -- 国家代码 (如: 86, 1, 44)
    local_number TEXT NOT NULL,           -- 本地号码 (如: 13812345678)
    whatsapp_id TEXT NOT NULL UNIQUE,     -- WhatsApp ID (如: <EMAIL>)
    display_number TEXT NOT NULL,         -- 显示号码 (如: +86 13812345678)
    is_verified INTEGER DEFAULT 0,        -- 是否已验证
    is_active INTEGER DEFAULT 1,          -- 是否活跃
    notes TEXT,                           -- 备注信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 核心组件

#### 1. TelenumberOperations (电话号码操作类)
- **位置**: `database/telenumber_operations.py`
- **功能**: 提供电话号码的CRUD操作
- **主要方法**:
  - `add_phone_number()`: 添加电话号码
  - `get_all_active_phone_numbers()`: 获取所有活跃号码
  - `get_whatsapp_ids_for_bulk_messaging()`: 获取群发用的WhatsApp ID列表
  - `search_phone_numbers()`: 搜索电话号码
  - `get_statistics()`: 获取统计信息

#### 2. BulkMessagingService (群发消息服务)
- **位置**: `chat/whatsapp/bulk_messaging_service.py`
- **功能**: 管理WhatsApp连接和群发消息
- **主要方法**:
  - `start()`: 启动WhatsApp服务
  - `stop()`: 停止WhatsApp服务
  - `send_bulk_messages()`: 发送群发消息
  - `get_status()`: 获取服务状态
  - `get_qr_code()`: 获取登录QR码

#### 3. BulkMessagingController (页面控制器)
- **位置**: `gui/uis/pages/page_bulk_messaging.py`
- **功能**: 管理UI交互和业务逻辑
- **主要功能**:
  - 电话号码表格显示和管理
  - WhatsApp登录状态管理
  - 消息内容编辑和验证
  - 群发按钮状态控制

## 使用指南

### 1. 启动应用
1. 运行主应用程序
2. 在左侧菜单中点击"群发获客"
3. 系统会自动加载群发获客页面

### 2. 管理电话号码

#### 添加电话号码
1. 点击"添加号码"按钮
2. 在弹出的对话框中：
   - 选择国家代码（支持搜索）
   - 输入本地电话号码
   - 添加备注信息（可选）
3. 点击"添加"完成

#### 查看和管理号码
- 电话号码列表显示所有已添加的号码
- 显示格式：`+国家代码 本地号码`
- 状态列显示号码是否活跃
- 验证列显示号码是否已验证
- 操作列提供删除等功能

### 3. WhatsApp登录

#### 首次登录
1. 点击"登录 WhatsApp"按钮
2. 系统会启动WhatsApp Web服务
3. 等待QR码生成（在浏览器中显示）
4. 使用手机WhatsApp扫描QR码
5. 登录成功后状态显示"已登录"

#### 会话管理
- 登录状态会自动保存
- 下次启动时可能无需重新扫码
- 如需切换账号，点击"断开连接"后重新登录

### 4. 群发消息

#### 编辑消息内容
1. 在"消息内容"区域输入要发送的消息
2. 系统会实时显示字符数
3. 支持多行文本和特殊字符

#### 发送群发消息
1. 确保WhatsApp已登录（状态显示"已登录"）
2. 确保已输入消息内容
3. 点击"开始群发"按钮
4. 确认发送对话框中的信息
5. 点击"是"开始群发

#### 监控发送进度
- 发送过程中会显示实时进度
- 显示格式：`正在群发 X/Y`
- 发送完成后显示成功和失败数量
- 详细结果保存在日志中

## 电话号码格式说明

### WhatsApp Web.js格式要求
WhatsApp Web.js要求电话号码格式为：`国家代码+本地号码@c.us`

**示例**：
- 中国号码：`<EMAIL>`
- 美国号码：`<EMAIL>`
- 英国号码：`<EMAIL>`

### 输入格式
用户输入时只需提供：
- **国家代码**：数字形式（如：86、1、44）
- **本地号码**：纯数字（如：13812345678）

系统会自动：
1. 清理输入中的非数字字符
2. 验证号码格式的有效性
3. 生成WhatsApp格式的ID
4. 创建用户友好的显示格式

### 支持的国家代码
常用国家代码包括：
- 中国：86
- 美国/加拿大：1
- 英国：44
- 日本：81
- 韩国：82
- 新加坡：65
- 马来西亚：60
- 泰国：66
- 印度：91
- 巴西：55
- 德国：49
- 法国：33
- 意大利：39
- 西班牙：34
- 俄罗斯：7
- 澳大利亚：61

## 注意事项和最佳实践

### 1. WhatsApp使用政策
- **遵守条款**：确保遵守WhatsApp的使用条款和服务协议
- **反垃圾邮件**：不要发送垃圾信息或骚扰消息
- **频率限制**：避免过于频繁的消息发送
- **内容合规**：确保消息内容符合相关法律法规

### 2. 技术限制
- **发送间隔**：系统自动在消息间添加3秒间隔
- **号码验证**：建议发送前验证号码是否在WhatsApp注册
- **网络要求**：需要稳定的网络连接
- **浏览器依赖**：依赖Puppeteer控制的Chrome浏览器

### 3. 安全建议
- **账号安全**：使用专门的WhatsApp账号进行群发
- **数据备份**：定期备份电话号码数据
- **权限控制**：限制群发功能的使用权限
- **日志监控**：定期检查发送日志和错误记录

### 4. 性能优化
- **批量管理**：一次性添加多个号码而非逐个添加
- **定期清理**：删除无效或重复的电话号码
- **状态更新**：及时更新号码的验证和活跃状态
- **错误处理**：处理发送失败的号码，避免重复尝试

## 故障排除

### 常见问题

#### 1. WhatsApp登录失败
**症状**：无法显示QR码或扫码后登录失败
**解决方案**：
- 检查网络连接
- 重启WhatsApp服务
- 清除浏览器缓存
- 确保手机WhatsApp版本最新

#### 2. 电话号码添加失败
**症状**：提示"添加电话号码失败"
**解决方案**：
- 检查号码格式是否正确
- 确认号码未重复添加
- 检查数据库连接状态
- 查看错误日志获取详细信息

#### 3. 群发消息失败
**症状**：消息发送失败或部分失败
**解决方案**：
- 确认WhatsApp登录状态
- 检查目标号码是否有效
- 验证消息内容格式
- 查看发送结果日志

#### 4. 页面加载异常
**症状**：群发获客页面无法正常显示
**解决方案**：
- 重启应用程序
- 检查数据库表是否存在
- 确认相关模块导入正常
- 查看应用程序日志

### 日志文件位置
- **应用日志**：`logs/` 目录
- **数据库文件**：`data/hermes.db`
- **WhatsApp数据**：`data/bulk_messaging/`

## 开发和扩展

### 添加新功能
1. **数据库扩展**：在`telenumber`表中添加新字段
2. **API扩展**：在`TelenumberOperations`类中添加新方法
3. **UI扩展**：在页面控制器中添加新的交互逻辑
4. **服务扩展**：在`BulkMessagingService`中添加新的WhatsApp功能

### 测试
运行测试脚本验证功能：
```bash
python test_bulk_messaging.py
```

### 部署注意事项
- 确保Node.js环境已安装
- 安装WhatsApp Web.js依赖：`npm install whatsapp-web.js`
- 配置数据库路径和权限
- 设置适当的日志级别

---

**版本**：1.0.0  
**更新日期**：2025-06-21  
**开发者**：Augment Agent
