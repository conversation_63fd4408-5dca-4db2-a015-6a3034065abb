"""
数据库管理器
提供SQLite数据库的连接、初始化和操作功能
"""
import os
import sqlite3
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any, Union

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self, db_path: str = "data/hermes.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径
        """
        self.db_path = self._get_absolute_path(db_path)
        self._ensure_dir_exists()
        self.conn = None
        self.cursor = None
    
    def _get_absolute_path(self, relative_path: str) -> str:
        """获取绝对路径"""
        project_root = Path(__file__).parent.parent
        return str(project_root / relative_path)
    
    def _ensure_dir_exists(self):
        """确保数据库目录存在"""
        os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
        
    def connect(self):
        """连接到数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            # 启用外键约束
            self.conn.execute("PRAGMA foreign_keys = ON")
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            logger.error(f"连接数据库时出错: {str(e)}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
            self.cursor = None
    
    def init_database(self):
        """初始化数据库表结构"""
        if not self.connect():
            return False
        
        try:
            # 用户表 - 新结构
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plg_usn TEXT UNIQUE NOT NULL,
                plg_pwd TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 店铺表 - 新结构
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS stores (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plg_status INTEGER DEFAULT 1,
                plg_shopname TEXT NOT NULL,
                plg_prompt TEXT,
                plg_apikey TEXT,
                plg_points INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (plg_shopname)
            )
            ''')
            
            # 上下文表 - 新表
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS contexts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_name TEXT NOT NULL,
                store_name TEXT NOT NULL,
                chat_id TEXT NOT NULL,
                context TEXT,
                context_trans TEXT,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (user_name, store_name, chat_id)
            )
            ''')
            
            # 意图分析结果表
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS intent_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_name TEXT NOT NULL,
                sender_id TEXT NOT NULL,
                stage TEXT NOT NULL,
                specific_intent TEXT NOT NULL,
                reasoning TEXT,
                source TEXT,
                message TEXT,
                context TEXT,
                manual INTEGER DEFAULT 0,
                username TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (store_name) REFERENCES stores(plg_shopname)
            )
            ''')

            # 电话号码表 - 用于群发获客功能
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS telenumber (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                country_code TEXT NOT NULL,
                local_number TEXT NOT NULL,
                whatsapp_id TEXT NOT NULL UNIQUE,
                display_number TEXT NOT NULL,
                is_verified INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            self.conn.commit()
            logger.info("数据库表结构初始化成功")
            return True
        except sqlite3.Error as e:
            logger.error(f"初始化数据库时出错: {str(e)}")
            self.conn.rollback()
            return False
        finally:
            self.close()
    
    def execute_query(self, query: str, params: Tuple = ()) -> List:
        """
        执行查询
        
        Args:
            query: SQL查询语句
            params: 查询参数
            
        Returns:
            List: 查询结果
        """
        if not self.connect():
            return []
        
        try:
            self.cursor.execute(query, params)
            result = self.cursor.fetchall()
            return result
        except sqlite3.Error as e:
            logger.error(f"执行查询时出错: {str(e)}")
            return []
        finally:
            self.close()
    
    def execute_update(self, query: str, params: Tuple = ()) -> bool:
        """
        执行更新操作
        
        Args:
            query: SQL更新语句
            params: 更新参数
            
        Returns:
            bool: 操作是否成功
        """
        if not self.connect():
            return False
        
        try:
            self.cursor.execute(query, params)
            self.conn.commit()
            return True
        except sqlite3.Error as e:
            logger.error(f"执行更新时出错: {str(e)}")
            self.conn.rollback()
            return False
        finally:
            self.close()
    
    def get_last_insert_id(self) -> int:
        """获取最后插入的ID"""
        if not self.conn:
            return -1
        
        try:
            cursor = self.conn.cursor()
            cursor.execute("SELECT last_insert_rowid()")
            return cursor.fetchone()[0]
        except sqlite3.Error as e:
            logger.error(f"获取最后插入ID时出错: {str(e)}")
            return -1
