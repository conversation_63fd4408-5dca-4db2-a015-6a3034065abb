"""
电话号码数据操作模块
提供telenumber表的CRUD操作，支持WhatsApp Web.js格式的电话号码管理
"""
import logging
import re
from typing import Dict, List, Tuple, Optional, Any
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class TelenumberOperations:
    """电话号码数据操作类"""
    
    def __init__(self, db_manager: DatabaseManager = None):
        """
        初始化电话号码数据操作
        
        Args:
            db_manager: 数据库管理器实例
        """
        self.db_manager = db_manager or DatabaseManager()
    
    def add_phone_number(self, country_code: str, local_number: str, notes: str = "") -> int:
        """
        添加新电话号码
        
        Args:
            country_code: 国家代码 (例如: "86", "1", "55")
            local_number: 本地号码 (例如: "13812345678")
            notes: 备注信息
            
        Returns:
            int: 新电话号码ID，失败返回-1
        """
        # 清理输入数据
        country_code = self._clean_country_code(country_code)
        local_number = self._clean_local_number(local_number)
        
        if not self._validate_phone_number(country_code, local_number):
            logger.error(f"无效的电话号码格式: {country_code} {local_number}")
            return -1
        
        # 生成WhatsApp ID格式
        whatsapp_id = f"{country_code}{local_number}@c.us"
        
        # 生成用户友好的显示格式
        display_number = self._format_display_number(country_code, local_number)
        
        # 检查号码是否已存在
        if self.get_phone_number_by_whatsapp_id(whatsapp_id):
            logger.warning(f"电话号码已存在: {whatsapp_id}")
            return -1
        
        query = """
        INSERT INTO telenumber (
            country_code, local_number, whatsapp_id, 
            display_number, notes
        )
        VALUES (?, ?, ?, ?, ?)
        """
        
        if not self.db_manager.connect():
            return -1
        
        try:
            self.db_manager.cursor.execute(query, (
                country_code, local_number, whatsapp_id, 
                display_number, notes
            ))
            self.db_manager.conn.commit()
            phone_id = self.db_manager.get_last_insert_id()
            logger.info(f"成功添加电话号码: {display_number} (ID: {phone_id})")
            return phone_id
        except Exception as e:
            logger.error(f"添加电话号码失败: {str(e)}")
            self.db_manager.conn.rollback()
            return -1
        finally:
            self.db_manager.close()
    
    def get_phone_number_by_id(self, phone_id: int) -> Optional[Dict]:
        """
        通过ID获取电话号码
        
        Args:
            phone_id: 电话号码ID
            
        Returns:
            Optional[Dict]: 电话号码信息字典
        """
        query = """
        SELECT id, country_code, local_number, whatsapp_id, 
               display_number, is_verified, is_active, notes,
               created_at, updated_at
        FROM telenumber
        WHERE id = ?
        """
        
        results = self.db_manager.execute_query(query, (phone_id,))
        if not results:
            return None
        
        return self._row_to_dict(results[0])
    
    def get_phone_number_by_whatsapp_id(self, whatsapp_id: str) -> Optional[Dict]:
        """
        通过WhatsApp ID获取电话号码
        
        Args:
            whatsapp_id: WhatsApp ID (例如: "<EMAIL>")
            
        Returns:
            Optional[Dict]: 电话号码信息字典
        """
        query = """
        SELECT id, country_code, local_number, whatsapp_id, 
               display_number, is_verified, is_active, notes,
               created_at, updated_at
        FROM telenumber
        WHERE whatsapp_id = ?
        """
        
        results = self.db_manager.execute_query(query, (whatsapp_id,))
        if not results:
            return None
        
        return self._row_to_dict(results[0])
    
    def get_all_active_phone_numbers(self) -> List[Dict]:
        """
        获取所有活跃的电话号码
        
        Returns:
            List[Dict]: 电话号码信息列表
        """
        query = """
        SELECT id, country_code, local_number, whatsapp_id, 
               display_number, is_verified, is_active, notes,
               created_at, updated_at
        FROM telenumber
        WHERE is_active = 1
        ORDER BY created_at DESC
        """
        
        results = self.db_manager.execute_query(query)
        phone_numbers = []
        
        for row in results:
            phone_numbers.append(self._row_to_dict(row))
        
        return phone_numbers
    
    def get_all_phone_numbers(self) -> List[Dict]:
        """
        获取所有电话号码（包括非活跃的）
        
        Returns:
            List[Dict]: 电话号码信息列表
        """
        query = """
        SELECT id, country_code, local_number, whatsapp_id, 
               display_number, is_verified, is_active, notes,
               created_at, updated_at
        FROM telenumber
        ORDER BY created_at DESC
        """
        
        results = self.db_manager.execute_query(query)
        phone_numbers = []
        
        for row in results:
            phone_numbers.append(self._row_to_dict(row))
        
        return phone_numbers
    
    def update_phone_number(self, phone_id: int, **kwargs) -> bool:
        """
        更新电话号码信息
        
        Args:
            phone_id: 电话号码ID
            **kwargs: 要更新的字段
            
        Returns:
            bool: 操作是否成功
        """
        allowed_fields = ['notes', 'is_verified', 'is_active']
        update_data = {k: v for k, v in kwargs.items() if k in allowed_fields}
        
        if not update_data:
            logger.warning("没有有效的更新字段")
            return False
        
        # 添加更新时间
        update_data['updated_at'] = 'CURRENT_TIMESTAMP'
        
        # 构建更新语句
        set_clause = ", ".join([
            f"{key} = ?" if key != 'updated_at' else f"{key} = CURRENT_TIMESTAMP"
            for key in update_data.keys()
        ])
        query = f"UPDATE telenumber SET {set_clause} WHERE id = ?"
        
        # 准备参数（排除updated_at，因为它使用CURRENT_TIMESTAMP）
        params = [v for k, v in update_data.items() if k != 'updated_at']
        params.append(phone_id)
        
        return self.db_manager.execute_update(query, tuple(params))
    
    def delete_phone_number(self, phone_id: int) -> bool:
        """
        删除电话号码
        
        Args:
            phone_id: 电话号码ID
            
        Returns:
            bool: 操作是否成功
        """
        query = "DELETE FROM telenumber WHERE id = ?"
        return self.db_manager.execute_update(query, (phone_id,))
    
    def get_whatsapp_ids_for_bulk_messaging(self) -> List[str]:
        """
        获取所有用于群发消息的WhatsApp ID列表

        Returns:
            List[str]: WhatsApp ID列表
        """
        query = """
        SELECT whatsapp_id
        FROM telenumber
        WHERE is_active = 1
        ORDER BY created_at DESC
        """

        results = self.db_manager.execute_query(query)
        return [row[0] for row in results]

    def search_phone_numbers(self, search_term: str) -> List[Dict]:
        """
        搜索电话号码

        Args:
            search_term: 搜索关键词

        Returns:
            List[Dict]: 匹配的电话号码列表
        """
        query = """
        SELECT id, country_code, local_number, whatsapp_id,
               display_number, is_verified, is_active, notes,
               created_at, updated_at
        FROM telenumber
        WHERE display_number LIKE ? OR local_number LIKE ? OR notes LIKE ?
        ORDER BY created_at DESC
        """

        search_pattern = f"%{search_term}%"
        results = self.db_manager.execute_query(query, (search_pattern, search_pattern, search_pattern))
        phone_numbers = []

        for row in results:
            phone_numbers.append(self._row_to_dict(row))

        return phone_numbers

    def get_phone_numbers_by_country(self, country_code: str) -> List[Dict]:
        """
        按国家代码获取电话号码

        Args:
            country_code: 国家代码

        Returns:
            List[Dict]: 该国家的电话号码列表
        """
        query = """
        SELECT id, country_code, local_number, whatsapp_id,
               display_number, is_verified, is_active, notes,
               created_at, updated_at
        FROM telenumber
        WHERE country_code = ?
        ORDER BY created_at DESC
        """

        results = self.db_manager.execute_query(query, (country_code,))
        phone_numbers = []

        for row in results:
            phone_numbers.append(self._row_to_dict(row))

        return phone_numbers

    def get_statistics(self) -> Dict:
        """
        获取电话号码统计信息

        Returns:
            Dict: 统计信息
        """
        stats = {
            'total_count': 0,
            'active_count': 0,
            'verified_count': 0,
            'countries': {}
        }

        # 总数统计
        query = "SELECT COUNT(*) FROM telenumber"
        results = self.db_manager.execute_query(query)
        if results:
            stats['total_count'] = results[0][0]

        # 活跃数量统计
        query = "SELECT COUNT(*) FROM telenumber WHERE is_active = 1"
        results = self.db_manager.execute_query(query)
        if results:
            stats['active_count'] = results[0][0]

        # 已验证数量统计
        query = "SELECT COUNT(*) FROM telenumber WHERE is_verified = 1"
        results = self.db_manager.execute_query(query)
        if results:
            stats['verified_count'] = results[0][0]

        # 按国家统计
        query = """
        SELECT country_code, COUNT(*) as count
        FROM telenumber
        GROUP BY country_code
        ORDER BY count DESC
        """
        results = self.db_manager.execute_query(query)
        for row in results:
            stats['countries'][row[0]] = row[1]

        return stats

    def batch_add_phone_numbers(self, phone_numbers: List[Tuple[str, str, str]]) -> Dict:
        """
        批量添加电话号码

        Args:
            phone_numbers: 电话号码列表，每个元素为(country_code, local_number, notes)

        Returns:
            Dict: 批量添加结果
        """
        results = {
            'success_count': 0,
            'failed_count': 0,
            'failed_numbers': [],
            'added_ids': []
        }

        for country_code, local_number, notes in phone_numbers:
            phone_id = self.add_phone_number(country_code, local_number, notes)
            if phone_id > 0:
                results['success_count'] += 1
                results['added_ids'].append(phone_id)
            else:
                results['failed_count'] += 1
                results['failed_numbers'].append(f"+{country_code} {local_number}")

        return results
    
    def _clean_country_code(self, country_code: str) -> str:
        """清理国家代码，移除非数字字符"""
        return re.sub(r'[^\d]', '', str(country_code))
    
    def _clean_local_number(self, local_number: str) -> str:
        """清理本地号码，移除非数字字符"""
        return re.sub(r'[^\d]', '', str(local_number))
    
    def _validate_phone_number(self, country_code: str, local_number: str) -> bool:
        """
        验证电话号码格式
        
        Args:
            country_code: 国家代码
            local_number: 本地号码
            
        Returns:
            bool: 是否有效
        """
        # 基本验证：国家代码1-4位数字，本地号码4-15位数字
        if not country_code or not local_number:
            return False
        
        if not (1 <= len(country_code) <= 4 and country_code.isdigit()):
            return False
        
        if not (4 <= len(local_number) <= 15 and local_number.isdigit()):
            return False
        
        return True
    
    def _format_display_number(self, country_code: str, local_number: str) -> str:
        """
        格式化显示号码为用户友好格式
        
        Args:
            country_code: 国家代码
            local_number: 本地号码
            
        Returns:
            str: 格式化的显示号码
        """
        return f"+{country_code} {local_number}"
    
    def _row_to_dict(self, row: Tuple) -> Dict:
        """将数据库行转换为字典"""
        return {
            'id': row[0],
            'country_code': row[1],
            'local_number': row[2],
            'whatsapp_id': row[3],
            'display_number': row[4],
            'is_verified': bool(row[5]),
            'is_active': bool(row[6]),
            'notes': row[7],
            'created_at': row[8],
            'updated_at': row[9]
        }
