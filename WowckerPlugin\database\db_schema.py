"""
数据库结构查看和重构脚本
用于重新设计SQLite数据库结构并提供查看功能
"""
import os
import sqlite3
import logging
from pathlib import Path
import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class DatabaseSchema:
    """数据库结构管理器类"""
    
    def __init__(self, db_path: str = "../data/hermes.db"):
        """
        初始化数据库管理器
        
        Args:
            db_path: 数据库文件路径，相对于当前脚本
        """
        current_dir = os.path.dirname(os.path.abspath(__file__))
        self.db_path = os.path.normpath(os.path.join(current_dir, db_path))
        self.conn = None
        self.cursor = None
        logger.info(f"数据库路径: {self.db_path}")
        
    def connect(self):
        """连接到数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path)
            # 启用外键约束
            self.conn.execute("PRAGMA foreign_keys = ON")
            self.cursor = self.conn.cursor()
            return True
        except sqlite3.Error as e:
            logger.error(f"连接数据库时出错: {str(e)}")
            return False
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
            self.conn = None
            self.cursor = None
    
    def rebuild_database(self):
        """重建数据库表结构"""
        if not self.connect():
            return False
        
        try:
            # 先备份原有数据（可选）
            # self.backup_existing_data()
            
            # 删除旧表
            logger.info("删除旧表结构...")
            self.cursor.execute("DROP TABLE IF EXISTS stores")
            self.cursor.execute("DROP TABLE IF EXISTS users")
            self.cursor.execute("DROP TABLE IF EXISTS contexts")
            self.cursor.execute("DROP TABLE IF EXISTS intent_analysis")
            self.cursor.execute("DROP TABLE IF EXISTS telenumber")
            
            # 新的用户表
            logger.info("创建用户表...")
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plg_usn TEXT UNIQUE NOT NULL,
                plg_pwd TEXT NOT NULL,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            # 新的店铺表
            logger.info("创建店铺表...")
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS stores (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                plg_status INTEGER DEFAULT 1,
                plg_shopname TEXT NOT NULL,
                plg_prompt TEXT,
                plg_apikey TEXT,
                plg_points INTEGER DEFAULT 0,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (plg_shopname)
            )
            ''')
            
            # 上下文表
            logger.info("创建上下文表...")
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS contexts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_name TEXT NOT NULL,
                store_name TEXT NOT NULL,
                chat_id TEXT NOT NULL,
                context TEXT,
                update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                UNIQUE (user_name, store_name, chat_id)
            )
            ''')
            
            # 意图分析结果表
            logger.info("创建意图分析结果表...")
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS intent_analysis (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_name TEXT NOT NULL,
                sender_id TEXT NOT NULL,
                stage TEXT NOT NULL,
                specific_intent TEXT NOT NULL,
                reasoning TEXT,
                source TEXT,
                message TEXT,
                context TEXT,
                manual INTEGER DEFAULT 0,
                username TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (store_name) REFERENCES stores(plg_shopname)
            )
            ''')

            # 电话号码表 - 用于群发获客功能
            logger.info("创建电话号码表...")
            self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS telenumber (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                country_code TEXT NOT NULL,
                local_number TEXT NOT NULL,
                whatsapp_id TEXT NOT NULL UNIQUE,
                display_number TEXT NOT NULL,
                is_verified INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')
            
            self.conn.commit()
            logger.info("数据库表结构重建成功")
            return True
        except sqlite3.Error as e:
            logger.error(f"重建数据库时出错: {str(e)}")
            self.conn.rollback()
            return False
        finally:
            self.close()
    
    def view_schema(self):
        """查看数据库表结构"""
        if not self.connect():
            return
        
        try:
            logger.info("数据库表结构:")
            
            # 获取所有表名
            self.cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = self.cursor.fetchall()
            
            for table in tables:
                table_name = table[0]
                if table_name == 'sqlite_sequence':
                    continue
                    
                logger.info(f"\n表名: {table_name}")
                
                # 获取表结构
                self.cursor.execute(f"PRAGMA table_info({table_name})")
                columns = self.cursor.fetchall()
                
                # 打印表的列信息
                print(f"\n表名: {table_name}")
                print("-" * 80)
                print(f"{'列名':<20}{'类型':<15}{'非空':<10}{'默认值':<20}{'主键':<10}")
                print("-" * 80)
                
                for col in columns:
                    col_name = col[1]
                    col_type = col[2]
                    not_null = "是" if col[3] == 1 else "否"
                    default_val = col[4] if col[4] is not None else ""
                    is_pk = "是" if col[5] == 1 else "否"
                    
                    print(f"{col_name:<20}{col_type:<15}{not_null:<10}{str(default_val):<20}{is_pk:<10}")
                
                # 统计表中的记录数
                self.cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = self.cursor.fetchone()[0]
                print(f"\n表 {table_name} 中共有 {count} 条记录")
                
                # 如果有数据，展示前5条
                if count > 0:
                    self.cursor.execute(f"SELECT * FROM {table_name} LIMIT 5")
                    rows = self.cursor.fetchall()
                    
                    # 获取列名
                    self.cursor.execute(f"PRAGMA table_info({table_name})")
                    columns = self.cursor.fetchall()
                    column_names = [col[1] for col in columns]
                    
                    print("\n数据示例:")
                    print("-" * 80)
                    print(" | ".join([f"{name:<15}" for name in column_names]))
                    print("-" * 80)
                    
                    for row in rows:
                        print(" | ".join([f"{str(value):<15}" for value in row]))
                print("\n")
                
        except sqlite3.Error as e:
            logger.error(f"查看数据库结构时出错: {str(e)}")
        finally:
            self.close()

    def insert_test_data(self):
        """插入测试数据"""
        if not self.connect():
            return False
        
        try:
            # 插入测试用户
            self.cursor.execute('''
            INSERT INTO users (plg_usn, plg_pwd) VALUES 
            ('user1', 'password1'),
            ('user2', 'password2')
            ''')
            
            # 插入测试店铺
            self.cursor.execute('''
            INSERT INTO stores (plg_status, plg_shopname, plg_prompt, plg_apikey, plg_points) VALUES 
            (1, 'Shop1', '智能助手提示词', 'sk-12345abcdef', 1000),
            (0, 'Shop2', '客服机器人提示词', 'sk-67890ghijkl', 500)
            ''')
            
            # 插入测试上下文
            current_time = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.cursor.execute('''
            INSERT INTO contexts (user_name, store_name, chat_id, context, context_trans, update_time) VALUES 
            ('user1', 'Shop1', 'chat123', '{"messages": [{"role": "user", "content": "你好"}, {"role": "assistant", "content": "您好，有什么可以帮助您的?"}]}', '', ?),
            ('user2', 'Shop2', 'chat456', '{"messages": [{"role": "user", "content": "产品价格是多少?"}, {"role": "assistant", "content": "我们的产品价格从100元起步，具体取决于您选择的型号。"}]}', '', ?)
            ''', (current_time, current_time))
            
            self.conn.commit()
            logger.info("测试数据插入成功")
            return True
        except sqlite3.Error as e:
            logger.error(f"插入测试数据时出错: {str(e)}")
            self.conn.rollback()
            return False
        finally:
            self.close()

def main():
    """主函数"""
    db_schema = DatabaseSchema()
    
    # 重建数据库
    if db_schema.rebuild_database():
        print("数据库结构重建成功！")
        
        # 插入测试数据
        if db_schema.insert_test_data():
            print("测试数据已插入！")
        
        # 查看数据库结构和数据
        db_schema.view_schema()
    else:
        print("数据库结构重建失败！")

if __name__ == "__main__":
    main()
