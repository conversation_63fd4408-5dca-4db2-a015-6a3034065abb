"""
数据库表结构确保模块
提供确保数据库表存在的功能
"""
import logging
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

def ensure_store_services_table_exists():
    """确保store_services表存在"""
    db_manager = DatabaseManager()
    
    try:
        if not db_manager.connect():
            logger.error("无法连接到数据库")
            return False
            
        # 检查表是否存在
        cursor = db_manager.cursor
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='store_services'")
        
        if not cursor.fetchone():
            logger.warning("store_services 表不存在，正在创建...")
            
            # 创建表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS store_services (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                store_id INTEGER NOT NULL,
                platform TEXT NOT NULL,
                is_running INTEGER DEFAULT 0,
                start_time TIMESTAMP,
                last_update TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (store_id) REFERENCES stores(id) ON DELETE CASCADE,
                UNIQUE (store_id, platform)
            )
            ''')
            
            db_manager.conn.commit()
            logger.info("store_services 表创建成功")
            return True
        
        return True
    except Exception as e:
        logger.exception(f"检查/创建 store_services 表时出错: {str(e)}")
        return False
    finally:
        db_manager.close()

def ensure_telenumber_table_exists():
    """确保telenumber表存在"""
    db_manager = DatabaseManager()

    try:
        if not db_manager.connect():
            logger.error("无法连接到数据库")
            return False

        # 检查表是否存在
        cursor = db_manager.cursor
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='telenumber'")

        if not cursor.fetchone():
            logger.warning("telenumber 表不存在，正在创建...")

            # 创建表
            cursor.execute('''
            CREATE TABLE IF NOT EXISTS telenumber (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                country_code TEXT NOT NULL,
                local_number TEXT NOT NULL,
                whatsapp_id TEXT NOT NULL UNIQUE,
                display_number TEXT NOT NULL,
                is_verified INTEGER DEFAULT 0,
                is_active INTEGER DEFAULT 1,
                notes TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
            ''')

            db_manager.conn.commit()
            logger.info("telenumber 表创建成功")
            return True

        return True
    except Exception as e:
        logger.exception(f"检查/创建 telenumber 表时出错: {str(e)}")
        return False
    finally:
        db_manager.close()

def initialize_all_tables():
    """初始化所有必要的表"""
    # 使用DatabaseManager初始化基础表
    db_manager = DatabaseManager()
    db_manager.init_database()

    # 确保store_services表存在
    ensure_store_services_table_exists()

    # 确保telenumber表存在
    ensure_telenumber_table_exists()

    logger.info("所有数据库表初始化完成")
