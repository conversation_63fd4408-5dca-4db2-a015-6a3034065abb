"""
WhatsApp群发消息服务
专门用于群发获客功能的WhatsApp服务实现
"""
import os
import sys
import json
import time
import logging
import threading
import subprocess
from typing import List, Dict, Any, Optional

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

from database.telenumber_operations import TelenumberOperations

logger = logging.getLogger(__name__)

class BulkMessagingService:
    """WhatsApp群发消息服务"""
    
    def __init__(self):
        """初始化群发消息服务"""
        self.current_dir = os.path.dirname(os.path.abspath(__file__))
        self.project_root = os.path.join(self.current_dir, '..', '..')
        self.node_script_path = os.path.join(self.current_dir, "whatsapp_bulk_service.js")
        
        # 创建群发专用的数据目录
        self.bulk_data_dir = os.path.join(self.project_root, "data", "bulk_messaging")
        os.makedirs(self.bulk_data_dir, exist_ok=True)
        
        # 服务状态
        self.is_running = False
        self.is_logged_in = False
        self.whatsapp_process = None
        
        # 群发状态
        self.is_sending = False
        self.send_progress = 0
        self.total_numbers = 0
        self.success_count = 0
        self.failed_count = 0
        
        # 数据库操作
        self.phone_ops = TelenumberOperations()
        
        # 线程锁
        self.status_lock = threading.Lock()
        
        # 创建Node.js脚本
        self._create_node_script()
    
    def _create_node_script(self):
        """创建Node.js群发服务脚本"""
        script_content = '''
const { Client, LocalAuth } = require('whatsapp-web.js');
const fs = require('fs');
const path = require('path');

// 获取环境变量
const BULK_DATA_DIR = process.env.BULK_DATA_DIR || './data/bulk_messaging';
const STATUS_FILE = path.join(BULK_DATA_DIR, 'status.json');
const SEND_QUEUE_FILE = path.join(BULK_DATA_DIR, 'send_queue.json');
const SEND_RESULTS_FILE = path.join(BULK_DATA_DIR, 'send_results.json');

// 确保数据目录存在
if (!fs.existsSync(BULK_DATA_DIR)) {
    fs.mkdirSync(BULK_DATA_DIR, { recursive: true });
}

// 初始化状态文件
function updateStatus(status) {
    const statusData = {
        timestamp: Date.now(),
        ...status
    };
    fs.writeFileSync(STATUS_FILE, JSON.stringify(statusData, null, 2));
}

// 创建WhatsApp客户端
const client = new Client({
    authStrategy: new LocalAuth({
        clientId: 'bulk-messaging',
        dataPath: path.join(BULK_DATA_DIR, 'auth')
    }),
    puppeteer: {
        headless: false,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    }
});

// 客户端事件处理
client.on('qr', (qr) => {
    console.log('QR Code received:', qr);
    updateStatus({
        status: 'qr_received',
        qr_code: qr,
        is_logged_in: false
    });
});

client.on('ready', () => {
    console.log('WhatsApp client is ready!');
    updateStatus({
        status: 'ready',
        is_logged_in: true
    });
    
    // 开始监听发送队列
    startSendQueueMonitor();
});

client.on('authenticated', () => {
    console.log('WhatsApp client authenticated');
    updateStatus({
        status: 'authenticated',
        is_logged_in: true
    });
});

client.on('auth_failure', (msg) => {
    console.error('Authentication failed:', msg);
    updateStatus({
        status: 'auth_failure',
        error: msg,
        is_logged_in: false
    });
});

client.on('disconnected', (reason) => {
    console.log('WhatsApp client disconnected:', reason);
    updateStatus({
        status: 'disconnected',
        reason: reason,
        is_logged_in: false
    });
});

// 发送队列监控
function startSendQueueMonitor() {
    setInterval(() => {
        if (fs.existsSync(SEND_QUEUE_FILE)) {
            try {
                const queueData = JSON.parse(fs.readFileSync(SEND_QUEUE_FILE, 'utf8'));
                if (queueData.action === 'send_bulk' && queueData.numbers && queueData.message) {
                    processBulkSend(queueData);
                    // 清空队列文件
                    fs.writeFileSync(SEND_QUEUE_FILE, '{}');
                }
            } catch (error) {
                console.error('Error processing send queue:', error);
            }
        }
    }, 1000);
}

// 处理群发消息
async function processBulkSend(queueData) {
    const { numbers, message } = queueData;
    const results = [];
    
    console.log(`Starting bulk send to ${numbers.length} numbers`);
    
    for (let i = 0; i < numbers.length; i++) {
        const number = numbers[i];
        try {
            // 发送消息
            await client.sendMessage(number, message);
            
            results.push({
                number: number,
                status: 'success',
                timestamp: Date.now()
            });
            
            console.log(`Message sent successfully to ${number} (${i + 1}/${numbers.length})`);
            
            // 更新进度
            updateStatus({
                status: 'sending',
                progress: i + 1,
                total: numbers.length,
                current_number: number
            });
            
            // 发送间隔（避免被限制）
            if (i < numbers.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 3000)); // 3秒间隔
            }
            
        } catch (error) {
            console.error(`Failed to send message to ${number}:`, error);
            results.push({
                number: number,
                status: 'failed',
                error: error.message,
                timestamp: Date.now()
            });
        }
    }
    
    // 保存发送结果
    fs.writeFileSync(SEND_RESULTS_FILE, JSON.stringify(results, null, 2));
    
    // 更新最终状态
    const successCount = results.filter(r => r.status === 'success').length;
    const failedCount = results.filter(r => r.status === 'failed').length;
    
    updateStatus({
        status: 'send_completed',
        total_sent: numbers.length,
        success_count: successCount,
        failed_count: failedCount
    });
    
    console.log(`Bulk send completed. Success: ${successCount}, Failed: ${failedCount}`);
}

// 启动客户端
console.log('Starting WhatsApp bulk messaging service...');
updateStatus({
    status: 'starting',
    is_logged_in: false
});

client.initialize();

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('Shutting down WhatsApp bulk messaging service...');
    updateStatus({
        status: 'shutting_down',
        is_logged_in: false
    });
    client.destroy();
    process.exit(0);
});
'''
        
        try:
            with open(self.node_script_path, 'w', encoding='utf-8') as f:
                f.write(script_content)
            logger.info(f"已创建Node.js群发服务脚本: {self.node_script_path}")
        except Exception as e:
            logger.error(f"创建Node.js脚本失败: {str(e)}")
    
    def start(self) -> bool:
        """启动群发消息服务"""
        try:
            if self.is_running:
                logger.warning("群发消息服务已在运行")
                return True
            
            logger.info("正在启动WhatsApp群发消息服务...")
            
            # 设置环境变量
            env = os.environ.copy()
            env["BULK_DATA_DIR"] = self.bulk_data_dir
            
            # 确保当前工作目录正确
            os.chdir(self.current_dir)
            
            # 启动Node.js进程
            node_cmd = ['node', self.node_script_path]
            
            # Windows环境处理
            if sys.platform == 'win32':
                try:
                    import win32process
                    creationflags = win32process.CREATE_NO_WINDOW
                except ImportError:
                    creationflags = 0x08000000  # CREATE_NO_WINDOW
            else:
                creationflags = 0
            
            self.whatsapp_process = subprocess.Popen(
                node_cmd,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                creationflags=creationflags
            )
            
            logger.info(f"已启动Node.js群发服务进程，PID: {self.whatsapp_process.pid}")
            
            # 启动输出监控线程
            self._start_output_monitor()
            
            # 启动状态监控线程
            self._start_status_monitor()
            
            self.is_running = True
            return True
            
        except Exception as e:
            logger.error(f"启动群发消息服务失败: {str(e)}")
            return False
    
    def stop(self) -> bool:
        """停止群发消息服务"""
        try:
            if self.whatsapp_process and self.whatsapp_process.poll() is None:
                logger.info(f"正在终止群发服务进程，PID: {self.whatsapp_process.pid}")
                self.whatsapp_process.terminate()
                
                try:
                    self.whatsapp_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    logger.warning("群发服务进程未能在5秒内终止，强制结束")
                    self.whatsapp_process.kill()
                
                self.whatsapp_process = None
            
            self.is_running = False
            self.is_logged_in = False
            logger.info("WhatsApp群发消息服务已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止群发消息服务失败: {str(e)}")
            return False
    
    def _start_output_monitor(self):
        """启动输出监控线程"""
        def monitor_output():
            while self.is_running and self.whatsapp_process:
                try:
                    line = self.whatsapp_process.stdout.readline()
                    if line:
                        logger.info(f"群发服务输出: {line.strip()}")
                except Exception as e:
                    logger.error(f"读取群发服务输出错误: {str(e)}")
                    break
        
        output_thread = threading.Thread(target=monitor_output)
        output_thread.daemon = True
        output_thread.start()
    
    def _start_status_monitor(self):
        """启动状态监控线程"""
        def monitor_status():
            status_file = os.path.join(self.bulk_data_dir, 'status.json')
            
            while self.is_running:
                try:
                    if os.path.exists(status_file):
                        with open(status_file, 'r', encoding='utf-8') as f:
                            status_data = json.load(f)
                            
                        with self.status_lock:
                            self.is_logged_in = status_data.get('is_logged_in', False)
                            
                            # 更新群发进度
                            if status_data.get('status') == 'sending':
                                self.is_sending = True
                                self.send_progress = status_data.get('progress', 0)
                                self.total_numbers = status_data.get('total', 0)
                            elif status_data.get('status') == 'send_completed':
                                self.is_sending = False
                                self.success_count = status_data.get('success_count', 0)
                                self.failed_count = status_data.get('failed_count', 0)
                    
                    time.sleep(1)
                    
                except Exception as e:
                    logger.error(f"监控状态文件错误: {str(e)}")
                    time.sleep(5)
        
        status_thread = threading.Thread(target=monitor_status)
        status_thread.daemon = True
        status_thread.start()
    
    def send_bulk_messages(self, message: str) -> bool:
        """发送群发消息"""
        try:
            # 检查详细状态而不是仅依赖内存中的状态
            detailed_status = self.get_detailed_status()
            node_status = detailed_status.get('node_status', 'unknown')

            if node_status != 'ready':
                logger.error(f"WhatsApp未就绪，当前状态: {node_status}，无法发送群发消息")
                return False
            
            # 获取所有活跃的WhatsApp ID
            whatsapp_ids = self.phone_ops.get_whatsapp_ids_for_bulk_messaging()
            
            if not whatsapp_ids:
                logger.error("没有可用的电话号码")
                return False
            
            # 创建发送队列
            queue_data = {
                'action': 'send_bulk',
                'numbers': whatsapp_ids,
                'message': message,
                'timestamp': time.time()
            }
            
            queue_file = os.path.join(self.bulk_data_dir, 'send_queue.json')
            with open(queue_file, 'w', encoding='utf-8') as f:
                json.dump(queue_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"已创建群发队列，目标号码数量: {len(whatsapp_ids)}")
            return True
            
        except Exception as e:
            logger.error(f"创建群发队列失败: {str(e)}")
            return False
    
    def get_status(self) -> Dict[str, Any]:
        """获取服务状态"""
        with self.status_lock:
            return {
                'is_running': self.is_running,
                'is_logged_in': self.is_logged_in,
                'is_sending': self.is_sending,
                'send_progress': self.send_progress,
                'total_numbers': self.total_numbers,
                'success_count': self.success_count,
                'failed_count': self.failed_count
            }
    
    def get_qr_code(self) -> Optional[str]:
        """获取QR码"""
        try:
            status_file = os.path.join(self.bulk_data_dir, 'status.json')
            if os.path.exists(status_file):
                with open(status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)
                return status_data.get('qr_code')
        except Exception as e:
            logger.error(f"获取QR码失败: {str(e)}")
        return None

    def reset_account(self) -> bool:
        """重置账号 - 清除登录信息并重新启动服务"""
        try:
            logger.info("开始重置WhatsApp账号...")

            # 停止当前服务
            self.stop()

            # 等待服务完全停止
            time.sleep(2)

            # 清除认证数据
            auth_dir = os.path.join(self.bulk_data_dir, 'auth')
            if os.path.exists(auth_dir):
                import shutil
                shutil.rmtree(auth_dir)
                logger.info("已清除认证数据")

            # 清除状态文件
            status_file = os.path.join(self.bulk_data_dir, 'status.json')
            if os.path.exists(status_file):
                os.remove(status_file)
                logger.info("已清除状态文件")

            # 重新启动服务
            if self.start():
                logger.info("账号重置成功，服务已重新启动")
                return True
            else:
                logger.error("账号重置后服务启动失败")
                return False

        except Exception as e:
            logger.error(f"重置账号失败: {str(e)}")
            return False

    def force_close(self) -> bool:
        """强制关闭服务和浏览器"""
        try:
            logger.info("强制关闭WhatsApp群发服务...")

            # 强制终止进程
            if self.whatsapp_process and self.whatsapp_process.poll() is None:
                try:
                    # 尝试优雅关闭
                    self.whatsapp_process.terminate()
                    self.whatsapp_process.wait(timeout=3)
                except subprocess.TimeoutExpired:
                    # 强制杀死进程
                    self.whatsapp_process.kill()
                    logger.warning("进程未能优雅关闭，已强制终止")

                self.whatsapp_process = None

            # 重置状态
            self.is_running = False
            self.is_logged_in = False
            self.is_sending = False

            # 清除状态文件
            status_file = os.path.join(self.bulk_data_dir, 'status.json')
            if os.path.exists(status_file):
                try:
                    os.remove(status_file)
                except:
                    pass

            logger.info("WhatsApp群发服务已强制关闭")
            return True

        except Exception as e:
            logger.error(f"强制关闭服务失败: {str(e)}")
            return False

    def get_detailed_status(self) -> Dict[str, Any]:
        """获取详细的服务状态"""
        try:
            status_file = os.path.join(self.bulk_data_dir, 'status.json')
            if os.path.exists(status_file):
                with open(status_file, 'r', encoding='utf-8') as f:
                    status_data = json.load(f)

                with self.status_lock:
                    return {
                        'is_running': self.is_running,
                        'is_logged_in': self.is_logged_in,
                        'is_sending': self.is_sending,
                        'send_progress': self.send_progress,
                        'total_numbers': self.total_numbers,
                        'success_count': self.success_count,
                        'failed_count': self.failed_count,
                        'node_status': status_data.get('status', 'unknown'),
                        'qr_code': status_data.get('qr_code'),
                        'timestamp': status_data.get('timestamp')
                    }
        except Exception as e:
            logger.error(f"获取详细状态失败: {str(e)}")

        # 返回基本状态
        return self.get_status()
