"""
测试WhatsApp群发服务的状态检测和账号管理功能
"""
import sys
import os
import time

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

from chat.whatsapp.bulk_messaging_service import BulkMessagingService

def test_service_lifecycle():
    """测试服务生命周期"""
    print("=== 测试WhatsApp群发服务生命周期 ===")
    
    # 创建服务实例
    service = BulkMessagingService()
    
    try:
        # 测试启动服务
        print("\n1. 测试启动服务...")
        if service.start():
            print("✓ 服务启动成功")
            
            # 等待一段时间让服务初始化
            print("等待服务初始化...")
            for i in range(10):
                time.sleep(1)
                status = service.get_detailed_status()
                print(f"  状态检查 {i+1}/10: {status['node_status']} (运行: {status['is_running']}, 登录: {status['is_logged_in']})")
                
                if status['node_status'] in ['ready', 'qr_received', 'auth_failure']:
                    break
            
            # 测试获取QR码
            print("\n2. 测试获取QR码...")
            qr_code = service.get_qr_code()
            if qr_code:
                print(f"✓ 获取到QR码: {qr_code[:50]}...")
            else:
                print("- 暂无QR码")
            
            # 测试重置账号
            print("\n3. 测试重置账号...")
            if service.reset_account():
                print("✓ 账号重置成功")
                
                # 等待重置后的状态
                time.sleep(3)
                status = service.get_detailed_status()
                print(f"  重置后状态: {status['node_status']}")
            else:
                print("✗ 账号重置失败")
            
            # 测试强制关闭
            print("\n4. 测试强制关闭...")
            if service.force_close():
                print("✓ 服务强制关闭成功")
            else:
                print("✗ 服务强制关闭失败")
                
        else:
            print("✗ 服务启动失败")
            
    except Exception as e:
        print(f"✗ 测试过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        # 确保服务被关闭
        try:
            service.force_close()
        except:
            pass
    
    print("\n=== 测试完成 ===")

def test_status_detection():
    """测试状态检测"""
    print("\n=== 测试状态检测 ===")
    
    service = BulkMessagingService()
    
    try:
        # 测试未启动状态
        print("1. 测试未启动状态...")
        status = service.get_detailed_status()
        print(f"  未启动状态: {status}")
        
        # 启动服务并监控状态变化
        print("\n2. 启动服务并监控状态变化...")
        if service.start():
            print("服务已启动，监控状态变化...")
            
            previous_status = None
            for i in range(30):  # 监控30秒
                time.sleep(1)
                current_status = service.get_detailed_status()
                
                if current_status['node_status'] != previous_status:
                    print(f"  [{i+1}s] 状态变化: {previous_status} -> {current_status['node_status']}")
                    previous_status = current_status['node_status']
                
                # 如果达到稳定状态就退出
                if current_status['node_status'] in ['ready', 'qr_received']:
                    print(f"  达到稳定状态: {current_status['node_status']}")
                    break
            
            # 显示最终状态
            final_status = service.get_detailed_status()
            print(f"\n最终状态:")
            print(f"  Node状态: {final_status['node_status']}")
            print(f"  运行状态: {final_status['is_running']}")
            print(f"  登录状态: {final_status['is_logged_in']}")
            print(f"  时间戳: {final_status.get('timestamp')}")
            
        else:
            print("✗ 服务启动失败")
            
    except Exception as e:
        print(f"✗ 状态检测测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
    
    finally:
        try:
            service.force_close()
        except:
            pass

def main():
    """主测试函数"""
    print("开始测试WhatsApp群发服务...")
    print("=" * 60)
    
    # 检查Node.js环境
    print("检查Node.js环境...")
    try:
        import subprocess
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✓ Node.js版本: {result.stdout.strip()}")
        else:
            print("✗ Node.js未安装或不可用")
            return
    except Exception as e:
        print(f"✗ 检查Node.js环境失败: {str(e)}")
        return
    
    # 检查whatsapp-web.js依赖
    print("检查whatsapp-web.js依赖...")
    try:
        import os
        node_modules_path = os.path.join(os.path.dirname(__file__), 'node_modules', 'whatsapp-web.js')
        if os.path.exists(node_modules_path):
            print("✓ whatsapp-web.js依赖已安装")
        else:
            print("✗ whatsapp-web.js依赖未安装")
            print("请运行: npm install whatsapp-web.js")
            return
    except Exception as e:
        print(f"✗ 检查依赖失败: {str(e)}")
        return
    
    # 运行测试
    test_status_detection()
    test_service_lifecycle()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("\n注意事项：")
    print("1. 如果看到QR码状态，说明服务正常工作")
    print("2. 扫码登录后状态应该变为'ready'")
    print("3. 重置账号会清除登录信息并重新生成QR码")
    print("4. 强制关闭会完全停止服务和浏览器进程")

if __name__ == "__main__":
    main()
